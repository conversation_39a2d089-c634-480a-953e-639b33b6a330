#!/bin/bash

# Script để chạy tự động python main.py với nhiều tài khoản
# Mỗi tài khoản chạy cách nhau 2 phút

# Kiểm tra xem file accounts.json có tồn tại không
if [ ! -f "accounts.json" ]; then
    echo "Error: File accounts.json không tồn tại!"
    exit 1
fi

# Kiểm tra xem file worker.py có tồn tại không
if [ ! -f "src/worker.py" ]; then
    echo "Error: File src/worker.py không tồn tại!"
    exit 1
fi

# Kiểm tra xem jq có được cài đặt không (để parse JSON)
if ! command -v jq &> /dev/null; then
    echo "Error: jq chưa được cài đặt. Vui lòng cài đặt jq để parse JSON."
    echo "Ubuntu/Debian: sudo apt-get install jq"
    echo "CentOS/RHEL: sudo yum install jq"
    echo "macOS: brew install jq"
    exit 1
fi

# Đọ<PERSON> số lượng tài khoản
account_count=$(jq length accounts.json)
echo "Tìm thấy $account_count tài khoản trong file accounts.json"
echo "Bắt đầu chạy script..."
echo "================================"

# Lặp qua từng tài khoản
for i in $(seq 0 $((account_count - 1))); do
    # Lấy email và password từ JSON
    email=$(jq -r ".[$i].email" accounts.json)
    password=$(jq -r ".[$i].password" accounts.json)
    
    echo "[$((i + 1))/$account_count] Đang chạy với tài khoản: $email"
    echo "Thời gian: $(date '+%Y-%m-%d %H:%M:%S')"
    
    # Chạy lệnh Python
    python src/worker.py --email "$email" --password "$password"
    
    # Kiểm tra kết quả
    if [ $? -eq 0 ]; then
        echo "✓ Hoàn thành thành công cho tài khoản: $email"
    else
        echo "✗ Lỗi khi chạy với tài khoản: $email"
    fi
    
    # Chờ 2 phút trước khi chạy tài khoản tiếp theo (trừ tài khoản cuối cùng)
    if [ $i -lt $((account_count - 1)) ]; then
        echo "Chờ 2 phút trước khi chạy tài khoản tiếp theo..."
        echo "--------------------------------"
        sleep 60
    fi
done

echo "================================"
echo "Hoàn thành tất cả tài khoản!"
echo "Thời gian kết thúc: $(date '+%Y-%m-%d %H:%M:%S')"
