# edge_rewards
<PERSON><PERSON> to automate daily Microsoft Rewards tasks using Edge. Searches and browsing to earn points automatically.

## Installation
```bash
pip install -r requirements.txt
```

## Usage
```bash
python main.py --email [email] --password [password]
```
### Options
- `--email`: Your Microsoft account email.
- `--password`: Your Microsoft account password.
- `--times`: The number of times to search and browse, defaults to 35.
- `--headless`: Run the browser in headless mode, defaults to False.
- `--manual`: Skip login, using current logged session, defaults to False.
- `--driver`: The type of chromedriver, options: "chrome", "firefox", "opera", "brave", "edge", defaults to "chrome".
- `--port`: The port of browser debugger, defaults to 9222.


*Note:* If you want to run the script without logging in, open browser in debugger mode, Set `driver` and `port` associated with your browser and run the script with `--manual` option. Make sure you have logged into your Microsoft Rewards account before running the script.