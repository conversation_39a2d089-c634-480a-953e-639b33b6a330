import json
import subprocess
import time
import schedule

from loguru import logger

from src.util.gg_chat_logging import post_message

ACCOUNT_CONFIG_PATH = 'account.json'

def read_config():
    with open(ACCOUNT_CONFIG_PATH, 'r') as f:
        return json.load(f)


def sync_gg_sheet(result: dict):
    logger.info("Start syncing google sheet...")
    logger.info("Sync google sheet finished")


def run_script(email: str, password: str):
    logger.info(f"Start running script with '{email}'...")
    point = "__"
    status = "failed"
    try:
        proc = subprocess.run(
            ["python", "src/main.py", "--email", email, "--password", password],
            check=True,
            capture_output=True,
            text=True,
        )

        lines = proc.stdout.splitlines()
        for line in lines:
            print(line)
            if "===RESULT===" in line:
                data_str = line.split("===RESULT===: ")[1].split("}")[0] + "}"
                result = json.loads(data_str)
                point = result['point']
                status = "success"
    except subprocess.CalledProcessError:
        point = "__"
        status = "failed"
        logger.error(f"Failed to run script with '{email}'")
    return {
        "email": email,
        "password": password,
        "point": point,
        "status": status
    }


def point_farm():
    logger.info("Start point farming...")
    accounts = read_config()
    for _ in range(10):
        for i, acc in enumerate(accounts):
            if not 'status' in acc or acc['status'] == "failed":
                result = run_script(acc['email'], acc['password'])
                accounts[i] = result
                sync_gg_sheet(result)
                time.sleep(90)
    logger.info("Point farm finished")
    post_message(f"Result {time.strftime('%d-%m-%Y %H:%M:%S')}:\n{json.dumps(accounts, indent=4)}")


def main():
    schedule.every().day.at("01:18").do(point_farm)
    logger.info("Start worker...")
    while True:
        schedule.run_pending()
        time.sleep(1)


if __name__ == "__main__":
    main()
