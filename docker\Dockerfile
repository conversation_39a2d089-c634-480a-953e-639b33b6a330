FROM python:3.10-slim-bullseye

# Disable writing .pyc files
ENV PYTHONDONTWRITEBYTECODE=1
# Enable fault handler
ENV PYTHONFAULTHANDLER=1
ENV PYTHONUNBUFFERED=1

# Prevents some interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    gcc \
    libpq-dev \
    netcat \
    sudo \
    wget \
    gnupg \
    unzip && \
    wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - && \
    echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list && \
    apt-get update && \
    apt-get install -y --no-install-recommends google-chrome-stable && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*
    
WORKDIR /app

COPY requirements.txt requirements.txt
RUN pip install --no-cache-dir -r requirements.txt && rm requirements.txt

COPY worker.py .
COPY src ./src
COPY .env .

ENTRYPOINT ["python", "worker.py"]
