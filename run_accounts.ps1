# PowerShell script to automatically run python main.py with multiple accounts
# Each account runs 2 minutes apart

# Check if accounts.json file exists
if (-not (Test-Path "accounts.json")) {
    Write-Host "Error: File accounts.json does not exist!" -ForegroundColor Red
    exit 1
}

# Check if main.py file exists
if (-not (Test-Path "src\main.py")) {
    Write-Host "Error: File main.py does not exist!" -ForegroundColor Red
    exit 1
}

# Read JSON file content
try {
    $accounts = Get-Content -Path "accounts.json" -Raw | ConvertFrom-Json
}
catch {
    Write-Host "Error: Cannot read accounts.json file. Make sure it is a valid JSON file." -ForegroundColor Red
    exit 1
}

$accountCount = $accounts.Length
Write-Host "Found $accountCount accounts in accounts.json file"
Write-Host "Starting script..."
Write-Host "================================"

# Loop through each account
for ($i = 0; $i -lt $accountCount; $i++) {
    $email = $accounts[$i].email
    $password = $accounts[$i].password

    Write-Host "[$($i + 1)/$accountCount] Running with account: $email" -ForegroundColor Cyan
    Write-Host "Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

    # Run Python command
    try {
        python src\main.py --email "$email" --password "$password"

        if ($LASTEXITCODE -eq 0) {
            Write-Host "Success for account: $email" -ForegroundColor Green
        }
        else {
            Write-Host "Error running account: $email" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "Error running account: $email - $($_.Exception.Message)" -ForegroundColor Red
    }

    # Wait 2 minutes before running next account (except for the last account)
    if ($i -lt ($accountCount - 1)) {
        Write-Host "Waiting 2 minutes before next account..." -ForegroundColor Yellow
        Write-Host "--------------------------------"
        Start-Sleep -Seconds 120
    }
}

Write-Host "================================"
Write-Host "All accounts completed!" -ForegroundColor Green
Write-Host "End time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
