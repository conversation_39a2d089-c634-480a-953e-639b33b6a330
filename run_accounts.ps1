# PowerShell script để chạy tự động python main.py với nhiều tài khoản
# Mỗi tài khoản chạy cách nhau 2 phút

# Kiểm tra xem file accounts.json có tồn tại không
if (-not (Test-Path "accounts.json")) {
    Write-Host "Error: File accounts.json không tồn tại!" -ForegroundColor Red
    exit 1
}

# Kiểm tra xem file main.py có tồn tại không
if (-not (Test-Path "main.py")) {
    Write-Host "Error: File main.py không tồn tại!" -ForegroundColor Red
    exit 1
}

# Đọc nội dung file JSON
try {
    $accounts = Get-Content -Path "accounts.json" -Raw | ConvertFrom-Json
}
catch {
    Write-Host "Error: Không thể đọc file accounts.json. Đảm bảo đây là file JSON hợp lệ." -ForegroundColor Red
    exit 1
}

$accountCount = $accounts.Length
Write-Host "Tìm thấy $accountCount tài khoản trong file accounts.json"
Write-Host "Bắt đầu chạy script..."
Write-Host "================================"

# Lặp qua từng tài khoản
for ($i = 0; $i -lt $accountCount; $i++) {
    $email = $accounts[$i].email
    $password = $accounts[$i].password
    
    Write-Host "[$($i + 1)/$accountCount] Đang chạy với tài khoản: $email" -ForegroundColor Cyan
    Write-Host "Thời gian: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
    
    # Chạy lệnh Python
    try {
        python main.py --email "$email" --password "$password"
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Hoàn thành thành công cho tài khoản: $email" -ForegroundColor Green
        }
        else {
            Write-Host "✗ Lỗi khi chạy với tài khoản: $email" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "✗ Lỗi khi chạy với tài khoản: $email - $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Chờ 2 phút trước khi chạy tài khoản tiếp theo (trừ tài khoản cuối cùng)
    if ($i -lt ($accountCount - 1)) {
        Write-Host "Chờ 2 phút trước khi chạy tài khoản tiếp theo..." -ForegroundColor Yellow
        Write-Host "--------------------------------"
        Start-Sleep -Seconds 120
    }
}

Write-Host "================================"
Write-Host "Hoàn thành tất cả tài khoản!" -ForegroundColor Green
Write-Host "Thời gian kết thúc: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
